-- Drop the table if it exists (replaced by regular view)
IF OBJECT_ID('mvwconvvoicesentimentdetaildata', 'U') IS NOT NULL
    DROP TABLE mvwconvvoicesentimentdetaildata;
IF OBJECT_ID('mvwconvvoicesentimentdetaildata', 'V') IS NOT NULL
    DROP VIEW mvwconvvoicesentimentdetaildata;
GO

-- Create regular view to replace the table
CREATE OR ALTER VIEW vwConvVoiceSentimentDetailData AS
SELECT
    ct.keyid,
    ct.conversationid,
    ct.starttime,
    ct.starttimeltc,
    ct.duration,
    ct.participant,
    ct.phrase,
    ct.sentiment,
    ct.phraseindex,
    ct.updated,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    cs.firstqueuename,
    cs.lastqueueid,
    cs.lastqueuename,
    cs.firstagentid,
    cs.firstagentname,
    cs.firstagentdepartment AS firstagentdept,
    cs.firstagentManager AS firstagentmanagerid,
    cs.firstagentManager AS firstagentmanagername,
    cs.lastagentid,
    cs.lastagentname,
    cs.lastagentdepartment AS lastagentdept,
    cs.lastagentManager AS lastagentmanagerid,
    cs.lastagentManager AS lastagentmanagername,
    cs.firstwrapupcode,
    cs.firstwrapname AS firstwrapupname,
    cs.lastwrapupcode,
    cs.lastwrapname AS lastwrapupname,
    dd.name AS divisionname
FROM convVoiceSentimentDetailData ct
LEFT JOIN vwConvSummaryData cs ON cs.conversationid = ct.conversationid
LEFT JOIN divisionDetails dd ON cs.divisionid = dd.id;
GO
