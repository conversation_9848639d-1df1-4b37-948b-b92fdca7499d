IF dbo.csg_table_exists('convSummaryData') = 0
CREATE TABLE [convSummaryData](
    [keyid] [nvarchar](100) NOT NULL,
    [conversationid] [nvarchar](50) NOT NULL,
    [conversationstartdate] [datetime],
    [conversationenddate] [datetime],
    [conversationstartdateltc] [datetime],
    [conversationenddateltc] [datetime],
    [originaldirection] [nvarchar](50),
    [firstmediatype] [nvarchar](50),
    [lastmediatype] [nvarchar](50),
    [peer] [nvarchar](50),
    [ani] [nvarchar](400),
    [dnis] [nvarchar](400),
    [firstagentid] [nvarchar](50),
    [lastagentid] [nvarchar](50),
    [firstqueueid] [nvarchar](50),
    [lastqueueid] [nvarchar](50),
    [ttalkcomplete] [int],
    [tqueuetime] [int],
    [tacw] [decimal](20, 0),
    [tansweredcount] [int],
    [tanswered] [decimal](20, 0),
    [tabandonedcount] [int],
    [tresponsecount] [int],
    [tresponse] [decimal](20, 0),
    [thandlecount] [int],
    [thandle] [decimal](20, 0),
    [firstwrapupcode] [nvarchar](255),
    [lastwrapupcode] [nvarchar](255),
    [theldcompletecount] [int],
    [theldcomplete] [decimal](20, 0),
    [nconsulttransferred] [int],
    [nblindtransferred] [int],
    [lastdisconnect] [nvarchar](50),
    [lastpurpose] [nvarchar](50),
    [lastsegmenttime] [int],
    [divisionid] [nvarchar](50),
    [divisionid2] [nvarchar](50),
    [divisionid3] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [convsummaryData_PK] PRIMARY KEY ([keyid])
);

IF dbo.csg_column_exists('convSummaryData', 'tabandonedcount') = 0
    ALTER TABLE convSummaryData ADD tabandonedcount INT;
ELSE
    ALTER TABLE convSummaryData ALTER COLUMN tabandonedcount INT;

IF dbo.csg_column_exists('convSummaryData', 'lastsegmenttime') = 0
    ALTER TABLE convSummaryData ADD lastsegmenttime [decimal](20, 2);
ELSE
    ALTER TABLE convSummaryData ALTER COLUMN lastsegmenttime [decimal](20, 2);

IF dbo.csg_column_exists('convSummaryData', 'ttalkcomplete') = 0
    ALTER TABLE convSummaryData ADD ttalkcomplete [decimal](20, 2);
ELSE
    ALTER TABLE convSummaryData ALTER COLUMN ttalkcomplete [decimal](20, 2);

IF dbo.csg_column_exists('convSummaryData', 'tqueuetime') = 0
    ALTER TABLE convSummaryData ADD tqueuetime [decimal](20, 2);
ELSE
    ALTER TABLE convSummaryData ALTER COLUMN tqueuetime [decimal](20, 2);

IF dbo.csg_index_exists('ConvSummaryDiscType', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryDiscType] ON [convSummaryData] ([lastdisconnect]);
IF dbo.csg_index_exists('ConvSummaryDivision', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryDivision] ON [convSummaryData] ([divisionid]);
IF dbo.csg_index_exists('ConvSummaryNwConvId', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryNwConvId] ON [convSummaryData] ([conversationid]);
IF dbo.csg_index_exists('ConvSummaryNwEndDate', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryNwEndDate] ON [convSummaryData] ([conversationenddate]);
IF dbo.csg_index_exists('ConvSummaryNwEndDateLTC', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryNwEndDateLTC] ON [convSummaryData] ([conversationenddateltc]);
IF dbo.csg_index_exists('ConvSummaryNwStartDate', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryNwStartDate] ON [convSummaryData] ([conversationstartdate]);
IF dbo.csg_index_exists('ConvSummaryNwStartDateLTC', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryNwStartDateLTC] ON [convSummaryData] ([conversationstartdateltc]);
IF dbo.csg_index_exists('ConvSummaryUserId', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryUserId] ON [convSummaryData] ([lastagentid]);
IF dbo.csg_index_exists('ConvSummaryWrap', 'convSummaryData') = 0
CREATE INDEX [ConvSummaryWrap] ON [convSummaryData] ([lastwrapupcode]);

-- Additional indexes for voice analytics views
IF dbo.csg_index_exists('convsummarydata_firstagentid_idx', 'convSummaryData') = 0
CREATE INDEX [convsummarydata_firstagentid_idx] ON [convSummaryData]([firstagentid]);

IF dbo.csg_index_exists('convsummarydata_firstqueueid_idx', 'convSummaryData') = 0
CREATE INDEX [convsummarydata_firstqueueid_idx] ON [convSummaryData]([firstqueueid]);

IF dbo.csg_index_exists('convsummarydata_lastqueueid_idx', 'convSummaryData') = 0
CREATE INDEX [convsummarydata_lastqueueid_idx] ON [convSummaryData]([lastqueueid]);