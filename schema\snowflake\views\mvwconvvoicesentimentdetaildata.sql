-- Drop any existing table (replaced by regular view)
DROP TABLE IF EXISTS mvwconvvoicesentimentdetaildata;

-- Create regular view to replace the table
CREATE OR REPLACE VIEW vwConvVoiceSentimentDetailData AS
SELECT
    ct.keyid,
    ct.conversationid,
    ct.starttime,
    ct.starttimeltc,
    ct.duration,
    ct.participant,
    ct.phrase,
    ct.sentiment,
    ct.phraseindex,
    ct.updated,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    cs.firstqueuename,
    cs.lastqueueid,
    cs.lastqueuename,
    cs.firstagentid,
    cs.firstagentname,
    cs.firstagentdepartment AS firstagentdept,
    cs.firstagentmanager AS firstagentmanagerid,
    cs.firstagentmanager AS firstagentmanagername,
    cs.lastagentid,
    cs.lastagentname,
    cs.lastagentdepartment AS lastagentdept,
    cs.lastagentmanager AS lastagentmanagerid,
    cs.lastagentmanager AS lastagentmanagername,
    cs.firstwrapupcode,
    cs.firstwrapname AS firstwrapupname,
    cs.lastwrapupcode,
    cs.lastwrapname AS lastwrapupname
FROM
    convvoicesentimentdetaildata ct
    LEFT JOIN vwconvsummarydata cs ON cs.conversationid :: text = ct.conversationid :: text;

COMMENT ON VIEW vwConvVoiceSentimentDetailData IS 'View for conversation voice sentiment detail data (converted from table)';

-- Backward compatibility alias for existing queries
CREATE OR REPLACE VIEW mvwconvvoicesentimentdetaildata AS
SELECT * FROM vwConvVoiceSentimentDetailData;

COMMENT ON VIEW mvwconvvoicesentimentdetaildata IS 'Backward compatibility alias for vwConvVoiceSentimentDetailData - DEPRECATED: Use vwConvVoiceSentimentDetailData instead';