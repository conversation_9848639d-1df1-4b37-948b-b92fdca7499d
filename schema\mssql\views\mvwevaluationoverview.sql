-- Backward compatibility alias for mvwevaluationoverview
-- This alias ensures existing queries continue to work after the mvw* to vw* conversion

-- Drop the table/view if it exists (replaced by regular view)
IF OBJECT_ID('mvwevaluationoverview', 'U') IS NOT NULL
    DROP TABLE mvwevaluationoverview;
IF OBJECT_ID('mvwevaluationoverview', 'V') IS NOT NULL
    DROP VIEW mvwevaluationoverview;
GO

-- Create alias view pointing to the new vwEvaluationOverview
-- Note: vwEvaluationOverview doesn't exist in MSSQL yet, this is a placeholder for future implementation
CREATE OR ALTER VIEW mvwevaluationoverview AS
SELECT 
    'PLACEHOLDER' AS message,
    'vwEvaluationOverview not yet implemented in MSSQL' AS note;
GO

-- Add extended property for documentation
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Backward compatibility alias for vwEvaluationOverview - DEPRECATED: Use vwEvaluationOverview instead. Currently placeholder until vwEvaluationOverview is implemented.',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwevaluationoverview';
GO
