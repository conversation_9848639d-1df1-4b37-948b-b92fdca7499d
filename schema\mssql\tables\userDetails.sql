IF dbo.csg_table_exists('userDetails') = 0
CREATE TABLE [userDetails](
    [id] [nvarchar](50) NOT NULL,
    [name] [nvarchar](255),
    [jabberId] [nvarchar](100),
    [state] [nvarchar](50),
    [title] [nvarchar](255),
    [email] [nvarchar](255),
    [username] [nvarchar](255),
    [department] [nvarchar](255),
    [manager] [nvarchar](50),
    [divisionid] [nvarchar](50),
    [employeeid] [nvarchar](50),
    [dateHire] [nvarchar](50),
    [updated] [datetime],
    CONSTRAINT [PK_userDetails] PRIMARY KEY ([id])
);

-- Indexes for evaluation view performance
IF dbo.csg_index_exists('userdetails_manager_idx', 'userDetails') = 0
CREATE INDEX [userdetails_manager_idx] ON [userDetails]([manager]);

IF dbo.csg_index_exists('userdetails_divisionid_idx', 'userDetails') = 0
CREATE INDEX [userdetails_divisionid_idx] ON [userDetails]([divisionid]);

IF dbo.csg_column_exists('userDetails', 'employeeid') = 0
ALTER TABLE dbo.userDetails ADD employeeid nvarchar(50) NULL;

IF dbo.csg_column_exists('userDetails', 'dateHire') = 0
ALTER TABLE dbo.userDetails ADD dateHire nvarchar(50) NULL;