IF dbo.csg_table_exists('evalData') = 0
CREATE TABLE evalData (
    keyid nvarchar(50) NOT NULL,
    conversationid nvarchar(50) NOT NULL,
    evaluationid nvarchar(50) NOT NULL,
    calibrationid nvarchar(50),
    evaluationformid nvarchar(50),
    evaluatorid nvarchar(50),
    userid nvarchar(50),
    status nvarchar(20),
    totalscore decimal(20, 2),
    averagescore decimal(20, 2),
    lowscore decimal(20, 2),
    highscore decimal(20, 2),
    totalcriticalscore decimal(20, 2),
    totalnoncriticalscore decimal(20, 2),
    agenthasread bit,
    releasedate datetime,
    releasedateltc datetime,
    assigneddate datetime,
    assigneddateltc datetime,
    updated datetime,
    CONSTRAINT [PK__evalData__607AFDE03D6A6394] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('evaldata_conv', 'evalData') = 0
CREATE INDEX [evaldata_conv] ON [evalData] ([conversationid]);
IF dbo.csg_index_exists('evaldata_evaluationid', 'evalData') = 0
CREATE INDEX [evaldata_evaluationid] ON [evalData] ([evaluationid]);

-- Additional indexes for evaluation view performance
IF dbo.csg_index_exists('evaldata_status_idx', 'evalData') = 0
CREATE INDEX [evaldata_status_idx] ON [evalData]([status]);

IF dbo.csg_index_exists('evaldata_userid_idx', 'evalData') = 0
CREATE INDEX [evaldata_userid_idx] ON [evalData]([userid]);

IF dbo.csg_index_exists('evaldata_evaluatorid_idx', 'evalData') = 0
CREATE INDEX [evaldata_evaluatorid_idx] ON [evalData]([evaluatorid]);

IF dbo.csg_index_exists('evaldata_releasedate_idx', 'evalData') = 0
CREATE INDEX [evaldata_releasedate_idx] ON [evalData]([releasedate]);

IF dbo.csg_index_exists('evaldata_assigneddate_idx', 'evalData') = 0
CREATE INDEX [evaldata_assigneddate_idx] ON [evalData]([assigneddate]);

-- Composite index for evaluation overview query optimization
IF dbo.csg_index_exists('evaldata_status_userid_evaluatorid_idx', 'evalData') = 0
CREATE INDEX [evaldata_status_userid_evaluatorid_idx] ON [evalData]([status], [userid], [evaluatorid]);

IF dbo.csg_column_exists('evalData', 'calibrationid') = 0
    ALTER TABLE evalData ADD calibrationid NVARCHAR(50);
ELSE
    ALTER TABLE evalData ALTER COLUMN calibrationid NVARCHAR(50);

IF dbo.csg_column_exists('evalData', 'averagescore') = 0
    ALTER TABLE evalData ADD averagescore DECIMAL(20, 2);
ELSE
    ALTER TABLE evalData ALTER COLUMN averagescore DECIMAL(20, 2);

IF dbo.csg_column_exists('evalData', 'lowscore') = 0
    ALTER TABLE evalData ADD lowscore DECIMAL(20, 2);
ELSE
    ALTER TABLE evalData ALTER COLUMN lowscore DECIMAL(20, 2);

IF dbo.csg_column_exists('evalData', 'highscore') = 0
    ALTER TABLE evalData ADD highscore DECIMAL(20, 2);
ELSE
    ALTER TABLE evalData ALTER COLUMN highscore DECIMAL(20, 2);