-- Drop the table if it exists (replaced by regular view)
IF OBJECT_ID('mvwconvvoiceoverviewdata', 'U') IS NOT NULL
    DROP TABLE mvwconvvoiceoverviewdata;
IF OBJECT_ID('mvwconvvoiceoverviewdata', 'V') IS NOT NULL
    DROP VIEW mvwconvvoiceoverviewdata;
GO

-- Create regular view to replace the table
CREATE OR ALTER VIEW vwConvVoiceOverviewData AS
SELECT
    cv.conversationid,
    cv.sentimentscore,
    cv.sentimenttrend,
    cv.agentdurationpercentage,
    cv.customerdurationpercentage,
    cv.silencedurationpercentage,
    cv.overtalkdurationpercentage,
    cv.overtalkcount,
    cv.ivrdurationpercentage,
    cv.acddurationpercentage,
    cv.otherdurationpercentage,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    cs.firstqueuename,
    cs.lastqueueid,
    cs.lastqueuename,
    cs.firstagentid,
    cs.firstagentname,
    cs.firstagentdepartment AS firstagentdept,
    cs.firstagentManager AS firstagentmanagerid,
    cs.firstagentManager AS firstagentmanagername,
    cs.lastagentid,
    cs.lastagentname,
    cs.lastagentdepartment AS lastagentdept,
    cs.lastagentManager AS lastagentmanagerid,
    cs.lastagentManager AS lastagentmanagername,
    cs.firstwrapupcode,
    cs.firstwrapname AS firstwrapupname,
    cs.lastwrapupcode,
    cs.lastwrapname AS lastwrapupname,
    dd.name AS divisionname
FROM convVoiceOverviewData cv
LEFT JOIN vwConvSummaryData cs ON cs.conversationid = cv.conversationid
LEFT JOIN divisionDetails dd ON cs.divisionid = dd.id;
GO

