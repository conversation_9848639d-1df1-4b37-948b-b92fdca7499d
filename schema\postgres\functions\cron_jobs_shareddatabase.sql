-- Template for cron
/*
SELECT
    cron.schedule(
        'Description',
        'Cron schedule (see: crontab.guru',
        'Command to run',
        'Database to process against'
    );
*/

SET
    search_path TO cron;

-- MVW cron jobs removed - converted to real-time views
-- The following cron jobs are no longer needed as MVW objects have been converted to regular views:
-- - mvwconvvoiceoverviewdata_customername (now vwConvVoiceOverviewData)
-- - mvwconvvoicetopicdetaildata_customername (now vwConvVoiceTopicDetailData)
-- - mvwconvvoicesentimentdetaildata_customername (now vwConvVoiceSentimentDetailData)
-- - mvwevaluationoverview_customername (now vwEvaluationOverview)
-- - mvwevaluationgroupdata_customername (now vwEvaluationGroupData)
-- - evaluationquestiondata_customername (now vwEvaluationQuestionData)
SELECT
    cron.schedule(
        'archivequeueinteraction_customername',
        '0 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archivequeueinteraction(0,''D'')'
    );

SELECT
    cron.schedule(
        'archiveuserinteraction_customername',
        '15 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archiveuserinteraction(0,''D'')'
    );

SELECT
    cron.schedule(
        'archiveuserpresence_customername',
        '30 0 */1 * *',
        'SET search_path TO contactcentredb; call contactcentredb.archiveuserpresence(0,''D'');'
    );

SELECT
    cron.schedule(
        'archivebacklog_customername',
        '0 1 * * *',
        'SET search_path TO contactcentredb; call contactcentredb.archivebacklog()'
    );
