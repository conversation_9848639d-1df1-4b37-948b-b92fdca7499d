-- Backward compatibility alias for mvwconvvoiceoverviewdata
-- This alias ensures existing queries continue to work after the mvw* to vw* conversion

-- Drop the table/view if it exists (replaced by regular view)
IF OBJECT_ID('mvwconvvoiceoverviewdata', 'U') IS NOT NULL
    DROP TABLE mvwconvvoiceoverviewdata;
IF OBJECT_ID('mvwconvvoiceoverviewdata', 'V') IS NOT NULL
    DROP VIEW mvwconvvoiceoverviewdata;
GO

-- Create alias view pointing to the new vwConvVoiceOverviewData
CREATE OR ALTER VIEW mvwconvvoiceoverviewdata AS
SELECT * FROM vwConvVoiceOverviewData;
GO

-- Add extended property for documentation
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Backward compatibility alias for vwConvVoiceOverviewData - DEPRECATED: Use vwConvVoiceOverviewData instead',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwconvvoiceoverviewdata';
GO
