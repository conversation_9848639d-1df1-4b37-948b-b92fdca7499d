-- Performance indexes for evaluation views
-- These indexes improve performance for vwEvaluationOverview, vwEvaluationQuestionData, and vwEvaluationGroupData
-- Note: <PERSON><PERSON><PERSON> doesn't use traditional indexes but these can be implemented as clustering keys or search optimization

-- 1. Search optimization for evaldata.status filtering (used heavily in vwEvaluationOverview)
-- ALTER TABLE evaldata ADD SEARCH OPTIMIZATION ON (status);

-- 2. Search optimization for evaldata.userid joins (used in all evaluation views)
-- ALTER TABLE evaldata ADD SEARCH OPTIMIZATION ON (userid);

-- 3. Search optimization for evaldata.evaluatorid joins (used in vwEvaluationOverview)
-- ALTER TABLE evaldata ADD SEARCH OPTIMIZATION ON (evaluatorid);

-- 4. Search optimization for evaldetails joins (used in vwEvaluationQuestionData)
-- ALTER TABLE evaldetails ADD SEARCH OPTIMIZATION ON (evaluationformid, questiongroupid, questionid, questionanwserid);

-- 5. Search optimization for evaldetails.questiongroupid (used in vwEvaluationGroupData subquery)
-- ALTER TABLE evaldetails ADD SEARCH OPTIMIZATION ON (questiongroupid);

-- 6. Search optimization for userdetails.manager (used in vwUserDetail which is used by evaluation views)
-- ALTER TABLE userdetails ADD SEARCH OPTIMIZATION ON (manager);

-- 7. Search optimization for userdetails.divisionid (used in vwUserDetail)
-- ALTER TABLE userdetails ADD SEARCH OPTIMIZATION ON (divisionid);

-- 8. Search optimization for evaldata date filtering (useful for date-based queries on views)
-- ALTER TABLE evaldata ADD SEARCH OPTIMIZATION ON (releasedate);
-- ALTER TABLE evaldata ADD SEARCH OPTIMIZATION ON (assigneddate);

-- Alternative: Clustering keys for better performance (uncomment as needed)
-- ALTER TABLE evaldata CLUSTER BY (status, userid, evaluatorid);
-- ALTER TABLE evaldetails CLUSTER BY (evaluationformid, questiongroupid);
-- ALTER TABLE userdetails CLUSTER BY (id, divisionid);

-- Note: Snowflake performance optimization recommendations:
-- 1. Enable search optimization service for frequently filtered columns
-- 2. Use clustering keys for tables with predictable access patterns
-- 3. Consider materialized views for complex aggregations (though we're moving away from this)
-- 4. Use result caching and warehouse auto-suspend for cost optimization

-- To enable search optimization (requires ACCOUNTADMIN or appropriate privileges):
-- ALTER TABLE evaldata ADD SEARCH OPTIMIZATION;
-- ALTER TABLE evaldetails ADD SEARCH OPTIMIZATION;
-- ALTER TABLE userdetails ADD SEARCH OPTIMIZATION;
-- ALTER TABLE evalquestiondata ADD SEARCH OPTIMIZATION;
-- ALTER TABLE evalquestiongroupdata ADD SEARCH OPTIMIZATION;
-- ALTER TABLE convsummarydata ADD SEARCH OPTIMIZATION;
