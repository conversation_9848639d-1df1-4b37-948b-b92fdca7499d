-- Template for cron
/*
SELECT
    cron.schedule_in_database(
        'Description',
        'Cron schedule (see: crontab.guru',
        'Command to run',
        'Database to process against'
    );
*/

SET
    search_path TO cron;

-- Remove any existing MVW cron jobs that are no longer needed
-- These jobs are removed because MVW objects have been converted to real-time views
SELECT cron.unschedule('mvwconvvoiceoverviewdata') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwconvvoiceoverviewdata');
SELECT cron.unschedule('mvwconvvoicetopicdetaildata') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwconvvoicetopicdetaildata');
SELECT cron.unschedule('mvwconvvoicesentimentdetaildata') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwconvvoicesentimentdetaildata');
SELECT cron.unschedule('mvwevaluationoverview') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwevaluationoverview');
SELECT cron.unschedule('mvwevaluationgroupdata') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'mvwevaluationgroupdata');
SELECT cron.unschedule('evaluationquestiondata') WHERE EXISTS (SELECT 1 FROM cron.job WHERE jobname = 'evaluationquestiondata');

-- MVW cron jobs removed - converted to real-time views
-- The following cron jobs are no longer needed as MVW objects have been converted to regular views:
-- - mvwconvvoiceoverviewdata (now vwConvVoiceOverviewData)
-- - mvwconvvoicetopicdetaildata (now vwConvVoiceTopicDetailData)
-- - mvwconvvoicesentimentdetaildata (now vwConvVoiceSentimentDetailData)
-- - mvwevaluationoverview (now vwEvaluationOverview)
-- - mvwevaluationgroupdata (now vwEvaluationGroupData)
-- - mvwevaluationquestiondata (now vwEvaluationQuestionData)
SELECT
    cron.schedule_in_database(
        'archivequeueinteraction',
        '0 0 */1 * *',
        'call archivequeueinteraction(0,''D'')',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'archiveuserinteraction',
        '15 0 */1 * *',
        'call archiveuserinteraction(0,''D'')',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'archiveuserpresence',
        '30 0 */1 * *',
        'call archiveuserpresence(0,''D'');',
        'contactcentredb'
    );

SELECT
    cron.schedule_in_database(
        'archivebacklog',
        '0 1 * * *',
        'call archivebacklog()',
        'contactcentredb'
    );
