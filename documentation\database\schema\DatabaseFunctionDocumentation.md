# Database Function Documentation

This document provides comprehensive documentation for all database functions used across different database types in the Genesys Adapter.

## MSSQL Functions

### Core Functions

#### `csg_table_exists(@tablename)`
Checks if a table exists in the current schema.

**Parameters:**
- `@tablename` (nvarchar(255)): The name of the table to check

**Returns:**
- `bit`: 1 if the table exists, 0 if it doesn't

**Example:**
```sql
IF dbo.csg_table_exists('userDetails') = 0
CREATE TABLE [userDetails](...);
```

#### `csg_index_exists(@indexname, @tablename)`
Checks if an index exists on a specific table.

**Parameters:**
- `@indexname` (nvarchar(255)): The name of the index to check
- `@tablename` (nvarchar(255)): The name of the table that should contain the index

**Returns:**
- `bit`: 1 if the index exists, 0 if it doesn't

**Example:**
```sql
IF dbo.csg_index_exists('IX_userDetails_userId', 'userDetails') = 0
CREATE INDEX [IX_userDetails_userId] ON [userDetails]([userId]);
```

#### `csg_view_definition_contains_string(@view_name, @expected_definition)`
Checks if a view's definition contains a specific string.

**Parameters:**
- `@view_name` (varchar(255)): The name of the view to check
- `@expected_definition` (varchar(max)): The string to search for in the view definition

**Returns:**
- `integer`: 1 if the definition contains the string, 0 if it doesn't, 2 if the view doesn't exist

**Example:**
```sql
IF dbo.csg_view_definition_contains_string('vwUserDetails', 'JOIN userGroups') = 0
-- Recreate the view with the correct definition
```

#### `csg_column_exists(@tablename, @columnname)`
Checks if a column exists in a specific table.

**Parameters:**
- `@tablename` (nvarchar(255)): The name of the table to check
- `@columnname` (nvarchar(255)): The name of the column to check

**Returns:**
- `bit`: 1 if the column exists, 0 if it doesn't

**Example:**
```sql
IF dbo.csg_column_exists('userDetails', 'email') = 0
ALTER TABLE userDetails ADD email [nvarchar](100);
```

### Additional Functions

#### `csg_constraint_exists(@constraintname, @tablename)`
Checks if a constraint exists on a specific table.

**Parameters:**
- `@constraintname` (nvarchar(255)): The name of the constraint to check
- `@tablename` (nvarchar(255)): The name of the table that should contain the constraint

**Returns:**
- `bit`: 1 if the constraint exists, 0 if it doesn't

**Example:**
```sql
IF dbo.csg_constraint_exists('FK_userDetails_userGroups', 'userDetails') = 0
ALTER TABLE [userDetails] ADD CONSTRAINT [FK_userDetails_userGroups] FOREIGN KEY ([groupId]) REFERENCES [userGroups] ([id]);
```

#### `csg_procedure_exists(@procedurename)`
Checks if a stored procedure exists.

**Parameters:**
- `@procedurename` (nvarchar(255)): The name of the procedure to check

**Returns:**
- `bit`: 1 if the procedure exists, 0 if it doesn't

**Example:**
```sql
IF dbo.csg_procedure_exists('ArchiveUserData') = 0
-- Create the procedure
```

#### `csg_recreate_index`
Safely drops and recreates an index.

**Parameters:**
- `@indexname` (nvarchar(255)): The name of the index to recreate
- `@tablename` (nvarchar(255)): The name of the table containing the index
- `@columnlist` (nvarchar(max)): The list of columns for the index
- `@is_unique` (bit, optional): Whether the index should be unique (default: 0)
- `@include_columns` (nvarchar(max), optional): Columns to include in the index (default: NULL)

**Example:**
```sql
EXEC dbo.csg_recreate_index 'IX_userDetails_userId', 'userDetails', 'userId', 1, 'name, email';
```

#### `csg_foreign_key_exists(@fkname, @tablename)`
Checks if a foreign key exists on a specific table.

**Parameters:**
- `@fkname` (nvarchar(255)): The name of the foreign key to check
- `@tablename` (nvarchar(255)): The name of the table that should contain the foreign key

**Returns:**
- `bit`: 1 if the foreign key exists, 0 if it doesn't

**Example:**
```sql
IF dbo.csg_foreign_key_exists('FK_userDetails_userGroups', 'userDetails') = 0
ALTER TABLE [userDetails] ADD CONSTRAINT [FK_userDetails_userGroups] FOREIGN KEY ([groupId]) REFERENCES [userGroups] ([id]);
```

### Archive Functions

#### `ArchiveQueueInteraction`
Archives queue interaction data to aggregated tables.

**Parameters:**
- `@AggOffSet` (INT): The offset for aggregation
- `@AggType` (NVARCHAR(1)): The aggregation type ('M' for monthly, 'W' for weekly, 'D' for daily)

#### `ArchiveUserInteraction`
Archives user interaction data to aggregated tables.

**Parameters:**
- `@AggOffSet` (INT): The offset for aggregation
- `@AggType` (NVARCHAR(1)): The aggregation type ('M' for monthly, 'W' for weekly, 'D' for daily)

#### `ArchiveUserPresence`
Archives user presence data to aggregated tables.

**Parameters:**
- `@AggOffSet` (INT): The offset for aggregation
- `@AggType` (NVARCHAR(1)): The aggregation type ('M' for monthly, 'W' for weekly, 'D' for daily)

## PostgreSQL Functions

### Core Functions

#### `csg_table_exists(tablename)`
Checks if a table exists in the current schema.

**Parameters:**
- `tablename` (varchar): The name of the table to check

**Returns:**
- `integer`: 1 if the table exists, 0 if it doesn't

**Example:**
```sql
SELECT csg_table_exists('user_details');
```

#### `csg_view_definition_contains_string(view_name, expected_definition)`
Checks if a view's definition contains a specific string.

**Parameters:**
- `view_name` (varchar): The name of the view to check
- `expected_definition` (varchar): The string to search for in the view definition

**Returns:**
- `integer`: 1 if the definition contains the string, 0 if it doesn't, 2 if the view doesn't exist

**Example:**
```sql
SELECT csg_view_definition_contains_string('vw_user_details', 'JOIN user_groups');
```

#### `csg_column_exists(tablename, columnname)`
Checks if a column exists in a specific table.

**Parameters:**
- `tablename` (varchar(255)): The name of the table to check
- `columnname` (varchar(255)): The name of the column to check

**Returns:**
- `boolean`: TRUE if the column exists, FALSE if it doesn't

**Example:**
```sql
SELECT csg_column_exists('user_details', 'email');
```

#### `get_preferred_schema()`
Returns the preferred schema from the current search path.

**Returns:**
- `TEXT`: The name of the preferred schema

**Example:**
```sql
SELECT get_preferred_schema();
```

### Additional Functions

#### `csg_index_exists(indexname, tablename)`
Checks if an index exists on a specific table.

**Parameters:**
- `indexname` (varchar): The name of the index to check
- `tablename` (varchar): The name of the table that should contain the index

**Returns:**
- `boolean`: TRUE if the index exists, FALSE if it doesn't

**Example:**
```sql
SELECT csg_index_exists('ix_user_details_user_id', 'user_details');
```

#### `csg_constraint_exists(constraintname, tablename)`
Checks if a constraint exists on a specific table.

**Parameters:**
- `constraintname` (varchar): The name of the constraint to check
- `tablename` (varchar): The name of the table that should contain the constraint

**Returns:**
- `boolean`: TRUE if the constraint exists, FALSE if it doesn't

**Example:**
```sql
SELECT csg_constraint_exists('fk_user_details_user_groups', 'user_details');
```

#### `csg_procedure_exists(procedurename)`
Checks if a procedure exists.

**Parameters:**
- `procedurename` (varchar): The name of the procedure to check

**Returns:**
- `boolean`: TRUE if the procedure exists, FALSE if it doesn't

**Example:**
```sql
SELECT csg_procedure_exists('archive_user_data');
```

#### `csg_recreate_index`
Safely drops and recreates an index.

**Parameters:**
- `indexname` (varchar): The name of the index to recreate
- `tablename` (varchar): The name of the table containing the index
- `columnlist` (varchar): The list of columns for the index
- `is_unique` (boolean, optional): Whether the index should be unique (default: false)
- `include_columns` (varchar, optional): Columns to include in the index (default: NULL)

**Example:**
```sql
CALL csg_recreate_index('ix_user_details_user_id', 'user_details', 'user_id', true, 'name, email');
```

#### `csg_create_partition`
Simplifies the creation of table partitions.

**Parameters:**
- `tablename` (varchar): The name of the table to partition
- `partition_column` (varchar): The column to partition on
- `partition_type` (varchar): The type of partitioning ('RANGE', 'LIST', or 'HASH')
- `partition_key` (varchar): For RANGE: 'date', 'integer', etc. For LIST: comma-separated values
- `partition_interval` (varchar, optional): For RANGE partitioning (default: '1 month')
- `partition_count` (integer, optional): Number of partitions to create ahead (default: 12)

**Example:**
```sql
CALL csg_create_partition('user_interactions', 'created_date', 'RANGE', 'date', '1 month', 24);
```

### Date/Time Functions

#### `getutcdate()`
Returns the current UTC date and time.

**Returns:**
- `timestamp without time zone`: The current UTC date and time

**Example:**
```sql
SELECT getutcdate();
```

#### `now_utc()`
Returns the current UTC date and time.

**Returns:**
- `timestamp without time zone`: The current UTC date and time

**Example:**
```sql
SELECT now_utc();
```

#### `timezonecalcs(tzset)`
Returns time zone calculation information.

**Parameters:**
- `tzset` (text): The time zone to calculate for

**Returns:**
- Table with columns:
  - `utctime` (timestamp without time zone): The current UTC time
  - `ltctime` (timestamp without time zone): The current local time
  - `diff` (integer): The difference in seconds between UTC and local time
  - `timezonechosen` (text): The time zone that was chosen

**Example:**
```sql
SELECT * FROM timezonecalcs('Australia/Sydney');
```

### Archive Functions

#### `archivebacklog()`
Archives data for multiple periods.

#### `archivequeueinteraction(aggoffset, aggtype)`
Archives queue interaction data to aggregated tables.

**Parameters:**
- `aggoffset` (integer): The offset for aggregation
- `aggtype` (character): The aggregation type ('M' for monthly, 'W' for weekly, 'D' for daily)

#### `archiveuserinteraction(aggoffset, aggtype)`
Archives user interaction data to aggregated tables.

**Parameters:**
- `aggoffset` (integer): The offset for aggregation
- `aggtype` (character): The aggregation type ('M' for monthly, 'W' for weekly, 'D' for daily)

#### `archiveuserpresence(aggoffset, aggtype)`
Archives user presence data to aggregated tables.

**Parameters:**
- `aggoffset` (integer): The offset for aggregation
- `aggtype` (character): The aggregation type ('M' for monthly, 'W' for weekly, 'D' for daily)

### Materialized View Functions (DEPRECATED)

**Note**: All materialized views have been converted to regular views for real-time data access. The following functions have been removed:

#### ~~`update_mvwconvvoiceoverviewdata()`~~ (REMOVED)
Previously updated the materialized view for conversation voice overview data. Now replaced by `vwConvVoiceOverviewData` real-time view.

#### ~~`update_mvwconvvoicetopicdetaildata()`~~ (REMOVED)
Previously updated the materialized view for conversation voice topic detail data. Now replaced by `vwConvVoiceTopicDetailData` real-time view.

#### ~~`update_mvwconvvoicesentimentdetaildata()`~~ (REMOVED)
Previously updated the materialized view for conversation voice sentiment detail data. Now replaced by `vwConvVoiceSentimentDetailData` real-time view.

#### ~~`update_mvwevaluationgroupdata()`~~ (REMOVED)
Previously updated the materialized view for evaluation group data. Now replaced by `vwEvaluationGroupData` real-time view.

### Real-Time Views

The following views provide real-time access to data that was previously available through materialized views:

#### Voice Analytics Views
- **`vwConvVoiceOverviewData`**: Real-time conversation voice overview data
- **`vwConvVoiceTopicDetailData`**: Real-time conversation voice topic detail data
- **`vwConvVoiceSentimentDetailData`**: Real-time conversation voice sentiment detail data

#### Evaluation Views
- **`vwEvaluationOverview`**: Real-time evaluation overview data
- **`vwEvaluationQuestionData`**: Real-time evaluation question data
- **`vwEvaluationGroupData`**: Real-time evaluation group data

#### Backward Compatibility
All original `mvw*` names are maintained as aliases pointing to the new views for backward compatibility.

## Snowflake Functions

### Core Functions

#### `get_current_timezone()`
Returns the current timezone.

**Returns:**
- `string`: The current timezone

**Example:**
```sql
SELECT get_current_timezone();
```

### Additional Functions

#### `csg_table_exists(tablename)`
Checks if a table exists in the current schema.

**Parameters:**
- `tablename` (STRING): The name of the table to check

**Returns:**
- `BOOLEAN`: TRUE if the table exists, FALSE if it doesn't

**Example:**
```sql
SELECT csg_table_exists('USER_DETAILS');
```

#### `csg_column_exists(tablename, columnname)`
Checks if a column exists in a specific table.

**Parameters:**
- `tablename` (STRING): The name of the table to check
- `columnname` (STRING): The name of the column to check

**Returns:**
- `BOOLEAN`: TRUE if the column exists, FALSE if it doesn't

**Example:**
```sql
SELECT csg_column_exists('USER_DETAILS', 'EMAIL');
```

#### `csg_constraint_exists(constraintname, tablename)`
Checks if a constraint exists on a specific table.

**Parameters:**
- `constraintname` (STRING): The name of the constraint to check
- `tablename` (STRING): The name of the table that should contain the constraint

**Returns:**
- `BOOLEAN`: TRUE if the constraint exists, FALSE if it doesn't

**Example:**
```sql
SELECT csg_constraint_exists('FK_USER_DETAILS_USER_GROUPS', 'USER_DETAILS');
```

#### `csg_convert_data_type`
Standardizes data type conversions.

**Parameters:**
- `tablename` (STRING): The name of the table containing the column
- `columnname` (STRING): The name of the column to convert
- `new_data_type` (STRING): The new data type for the column

**Returns:**
- `STRING`: A message indicating the result of the operation

**Example:**
```sql
CALL csg_convert_data_type('USER_DETAILS', 'EMAIL', 'VARCHAR(255)');
```

#### `csg_create_or_replace_table`
Safely creates or replaces a table.

**Parameters:**
- `tablename` (STRING): The name of the table to create or replace
- `table_definition` (STRING): The SQL definition for the table

**Returns:**
- `STRING`: A message indicating the result of the operation

**Example:**
```sql
CALL csg_create_or_replace_table('USER_DETAILS', '(ID VARCHAR(50) PRIMARY KEY, NAME VARCHAR(100), EMAIL VARCHAR(255))');
```

### JavaScript Stored Procedures

#### `check_and_update_adherenceactdata()`
Checks and updates adherence activity data.

**Returns:**
- `STRING`: A message indicating the result of the operation

#### `check_and_update_adherencedaydata()`
Checks and updates adherence day data.

**Returns:**
- `STRING`: A message indicating the result of the operation

#### `check_and_update_adherenceexcdata()`
Checks and updates adherence exception data.

**Returns:**
- `STRING`: A message indicating the result of the operation

#### `check_and_update_suboverviewdata()`
Checks and updates subscription overview data.

**Returns:**
- `STRING`: A message indicating the result of the operation

#### `check_and_drop_table()`
Checks and drops a table if it contains quoted identifiers.

**Returns:**
- `STRING`: A message indicating the result of the operation
