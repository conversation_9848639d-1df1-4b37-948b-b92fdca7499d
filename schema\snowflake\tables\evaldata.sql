
CREATE TABLE IF NOT EXISTS evaldata (
    keyid varchar(100) NOT NULL,
    conversationid varchar(50) NOT NULL,
    evaluationid varchar(50) NOT NULL,
    calibrationid varchar(50),
    evaluationformid varchar(50),
    evaluatorid varchar(50),
    userid varchar(50),
    status varchar(20),
    totalscore numeric(20, 2),
    averagescore numeric(20, 2),
    lowscore numeric(20, 2),
    highscore numeric(20, 2),
    totalcriticalscore numeric(20, 2),
    totalnoncriticalscore numeric(20, 2),
    agenthasread number,
    releasedate timestamp without time zone,
    releasedateltc timestamp without time zone,
    assigneddate timestamp without time zone,
    assigneddateltc timestamp without time zone,
    updated timestamp without time zone,
    CONSTRAINT evaldata_pkey PRIMARY KEY (keyid)
);

-- Note: Snowflake doesn't support traditional indexes like PostgreSQL/MSSQL
-- Instead, consider using clustering keys or search optimization for performance
-- Uncomment and modify as needed based on query patterns:

-- ALTER TABLE evaldata CLUSTER BY (status, userid, evaluatorid);
-- ALTER TABLE evaldata ADD SEARCH OPTIMIZATION ON (status, userid, evaluatorid, releasedate, assigneddate);

ALTER TABLE evaldata ALTER COLUMN calibrationid DROP NOT NULL;
