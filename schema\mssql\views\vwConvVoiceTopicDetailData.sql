
-- Create regular view to replace the table
CREATE OR ALTER VIEW vwConvVoiceTopicDetailData AS
SELECT
    ct.keyid,
    ct.conversationid,
    ct.starttime,
    ct.starttimeltc,
    ct.participant,
    ct.duration,
    ct.confidence,
    ct.topicname,
    ct.topicid,
    ct.topicphrase,
    ct.transcriptphrase,
    ct.updated,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    cs.firstqueuename,
    cs.lastqueueid,
    cs.lastqueuename,
    cs.firstagentid,
    cs.firstagentname,
    cs.firstagentdepartment AS firstagentdept,
    cs.firstagentManager AS firstagentmanagerid,
    cs.firstagentManager AS firstagentmanagername,
    cs.lastagentid,
    cs.lastagentname,
    cs.lastagentdepartment AS lastagentdept,
    cs.lastagentManager AS lastagentmanagerid,
    cs.lastagentManager AS lastagentmanagername,
    cs.firstwrapupcode,
    cs.firstwrapname AS firstwrapupname,
    cs.lastwrapupcode,
    cs.lastwrapname AS lastwrapupname,
    dd.name AS divisionname
FROM convVoiceTopicDetailData ct
LEFT JOIN vwConvSummaryData cs ON cs.conversationid = ct.conversationid
LEFT JOIN divisionDetails dd ON cs.divisionid = dd.id;
GO
