-- Drop the materialized view/table if it exists (replaced by regular view)
DROP MATERIALIZED VIEW IF EXISTS mvwconvvoiceoverviewdata;
DROP TABLE IF EXISTS mvwconvvoiceoverviewdata;

-- Create regular view to replace the materialized view/table
CREATE OR REPLACE VIEW vwConvVoiceOverviewData AS
SELECT
    cv.conversationid,
    cv.sentimentscore,
    cv.sentimenttrend,
    cv.agentdurationpercentage,
    cv.customerdurationpercentage,
    cv.silencedurationpercentage,
    cv.overtalkdurationpercentage,
    cv.overtalkcount,
    cv.ivrdurationpercentage,
    cv.acddurationpercentage,
    cv.otherdurationpercentage,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    cs.firstqueuename,
    cs.lastqueueid,
    cs.lastqueuename,
    cs.firstagentid,
    cs.firstagentname,
    cs.firstagentdepartment AS firstagentdept,
    cs.firstagentmanager AS firstagentmanagerid,
    cs.firstagentmanager AS firstagentmanagername,
    cs.lastagentid,
    cs.lastagentname,
    cs.lastagentdepartment AS lastagentdept,
    cs.lastagentmanager AS lastagentmanagerid,
    cs.lastagentmanager AS lastagentmanagername,
    cs.firstwrapupcode,
    cs.firstwrapname AS firstwrapupname,
    cs.lastwrapupcode,
    cs.lastwrapname AS lastwrapupname,
    dd.name AS divisionname
FROM convvoiceoverviewdata cv
LEFT JOIN vwconvsummarydata cs ON cs.conversationid::text = cv.conversationid::text
LEFT JOIN divisiondetails dd ON cs.divisionid::text = dd.id::text;

COMMENT ON VIEW vwConvVoiceOverviewData IS 'View for conversation voice overview data (converted from materialized view)';

-- Backward compatibility alias for existing queries
CREATE OR REPLACE VIEW mvwconvvoiceoverviewdata AS
SELECT * FROM vwConvVoiceOverviewData;

COMMENT ON VIEW mvwconvvoiceoverviewdata IS 'Backward compatibility alias for vwConvVoiceOverviewData - DEPRECATED: Use vwConvVoiceOverviewData instead';
