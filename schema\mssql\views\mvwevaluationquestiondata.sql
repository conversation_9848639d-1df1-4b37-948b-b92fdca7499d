-- Backward compatibility alias for mvwevaluationquestiondata
-- This alias ensures existing queries continue to work after the mvw* to vw* conversion

-- Drop the table/view if it exists (replaced by regular view)
IF OBJECT_ID('mvwevaluationquestiondata', 'U') IS NOT NULL
    DROP TABLE mvwevaluationquestiondata;
IF OBJECT_ID('mvwevaluationquestiondata', 'V') IS NOT NULL
    DROP VIEW mvwevaluationquestiondata;
GO

-- Create alias view pointing to the existing vwEvalQuestionData (closest equivalent)
CREATE OR ALTER VIEW mvwevaluationquestiondata AS
SELECT * FROM vwEvalQuestionData;
GO

-- Add extended property for documentation
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Backward compatibility alias for evaluation question data - DEPRECATED: Use vwEvalQuestionData instead',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwevaluationquestiondata';
GO
