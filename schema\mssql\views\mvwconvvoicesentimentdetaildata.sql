-- Backward compatibility alias for mvwconvvoicesentimentdetaildata
-- This alias ensures existing queries continue to work after the mvw* to vw* conversion

-- Drop the table/view if it exists (replaced by regular view)
IF OBJECT_ID('mvwconvvoicesentimentdetaildata', 'U') IS NOT NULL
    DROP TABLE mvwconvvoicesentimentdetaildata;
IF OBJECT_ID('mvwconvvoicesentimentdetaildata', 'V') IS NOT NULL
    DROP VIEW mvwconvvoicesentimentdetaildata;
GO

-- Create alias view pointing to the new vwConvVoiceSentimentDetailData
CREATE OR ALTER VIEW mvwconvvoicesentimentdetaildata AS
SELECT * FROM vwConvVoiceSentimentDetailData;
GO

-- Add extended property for documentation
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Backward compatibility alias for vwConvVoiceSentimentDetailData - DEPRECATED: Use vwConvVoiceSentimentDetailData instead',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwconvvoicesentimentdetaildata';
GO
