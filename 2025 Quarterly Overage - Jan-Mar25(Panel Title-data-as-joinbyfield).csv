﻿Customer Name,Start Date,End Date,Primary Date,License Name,Prepay,Contracted,Usage,Overage,,Price/unit,Total,,Total Invoice,,,,,,
AFCA,8/12/2024 0:00,7/01/2025 23:59,"December, 2024",Genesys Cloud Collaborate User,0,0,1,1,,,,,,,,,,,
AFCA,8/01/2025 0:00,7/02/2025 23:59,"January, 2025",Genesys Cloud Collaborate User,0,0,1,1,,,,,,,,,,,
AFCA,8/02/2025 0:00,7/03/2025 23:59,"February, 2025",Genesys Cloud Collaborate User,0,0,1,1,,,,,,,,,,,
AFCA,8/03/2025 0:00,7/04/2025 23:59,"March, 2025",Genesys Cloud Collaborate User,0,0,1,1,,,,,,,,,,,
AFCA,8/12/2024 0:00,7/01/2025 23:59,"December, 2024",Genesys Cloud Communicate User,650,650,1059,409,,2.5,1022.5,,,,,,,,
AFCA,8/01/2025 0:00,7/02/2025 23:59,"January, 2025",Genesys Cloud Communicate User,650,650,1075,425,,2.5,1062.5,,,,,,,,
AFCA,8/02/2025 0:00,7/03/2025 23:59,"February, 2025",Genesys Cloud Communicate User,650,650,1094,444,,2.5,1110,,,,,,,,
AFCA,8/03/2025 0:00,7/04/2025 23:59,"March, 2025",Genesys Cloud Communicate User,967,967,1099,132,,,,,,*Matt please ensure AFCA contracted figures are hard coded to 650 for this one,,,,,
AFCA,8/12/2024 0:00,7/01/2025 23:59,"December, 2024",Genesys Cloud CX 3,63,63,104,41,,8,328,,,,,,,,
AFCA,8/01/2025 0:00,7/02/2025 23:59,"January, 2025",Genesys Cloud CX 3,63,63,103,40,,8,320,,,,,,,,
AFCA,8/02/2025 0:00,7/03/2025 23:59,"February, 2025",Genesys Cloud CX 3,63,63,102,39,,8,312,,4155,*Matt please ensure AFCA contracted figures are hard coded to 63 for this one,,,,,
AFCA,8/03/2025 0:00,7/04/2025 23:59,"March, 2025",Genesys Cloud CX 3,63,63,101,38,,,,,,,,,,,
Brickworks,12/12/2024 0:00,11/01/2025 23:59,"December, 2024",Genesys Cloud CX 1,13,50,11,-39,,,,,,,,,,,
Brickworks,12/01/2025 0:00,11/02/2025 23:59,"January, 2025",Genesys Cloud CX 1,13,50,9,-41,,,,,,,,,,,
Brickworks,12/02/2025 0:00,11/03/2025 23:59,"February, 2025",Genesys Cloud CX 1,13,50,7,-43,,,,,,,,,,,
Brickworks,12/02/2025 0:00,11/03/2025 23:59,"February, 2025",Genesys Cloud CX 1,13,50,8,-42,,,,,,,,,,,
Compassion,16/12/2024 0:00,15/01/2025 23:59,"December, 2024",Genesys Cloud Collaborate User,1,1,0,-1,,,,,,,,,,,
Compassion,16/01/2025 0:00,15/02/2025 23:59,"January, 2025",Genesys Cloud Collaborate User,1,1,0,-1,,,,,,,,,,,
Compassion,16/02/2025 0:00,15/03/2025 23:59,"March, 2025",Genesys Cloud Collaborate User,1,1,0,-1,,,,,,,,,,,
Compassion,16/12/2024 0:00,15/01/2025 23:59,"December, 2024",Genesys Cloud Communicate User,12,12,5,-7,,,,,,,,,,,
Compassion,16/01/2025 0:00,15/02/2025 23:59,"January, 2025",Genesys Cloud Communicate User,12,12,5,-7,,,,,,,,,,,
Compassion,16/02/2025 0:00,15/03/2025 23:59,"March, 2025",Genesys Cloud Communicate User,12,12,4,-8,,,,,,,,,,,
Compassion,16/12/2024 0:00,15/01/2025 23:59,"December, 2024",Genesys Cloud CX 3,38,38,34,-4,,,,,,*Matt please hard code the contracted figure of 50,,,,,
Compassion,16/01/2025 0:00,15/02/2025 23:59,"January, 2025",Genesys Cloud CX 3,38,38,33,-5,,,,,,,,,,,
Compassion,16/02/2025 0:00,15/03/2025 23:59,"March, 2025",Genesys Cloud CX 3,38,38,34,-4,,,,,,,,,,,
Datacom/Communities,,7/10/2024 0:00,,Genesys Cloud CX 3 Concurrent,,80,87,7,,19.2,134.4,,,,,,,,
Datacom/Communities,,7/11/2024 0:00,,Genesys Cloud CX 3 Concurrent,,80,82,2,,19.2,38.4,,,,,,,,
Datacom/Communities,,7/12/2024 0:00,,Genesys Cloud CX 3 Concurrent,,80,81,1,,19.2,19.2,,,,,,,,
Datacom/Communities,8/12/2024 0:00,7/01/2025 23:59,"December, 2024",Genesys Cloud CX 3 Concurrent,85,80,82,2,,19.2,38.4,,,,,,,,
Datacom/Communities,8/01/2025 0:00,7/02/2025 23:59,"January, 2025",Genesys Cloud CX 3 Concurrent,85,80,83,3,,19.2,57.6,,,,,,,,
Datacom/Communities,8/02/2025 0:00,7/03/2025 23:59,"February, 2025",Genesys Cloud CX 3 Concurrent,85,80,92,12,,19.2,230.4,,518.4,*Matt please ensure DOC contracted figures are hard coded to 80 for this one,,,,,
Datacom/Communities,8/03/2025 0:00,7/04/2025 23:59,"March, 2025",Genesys Cloud CX 3 Concurrent,85,80,88,8,,,,,,,,,,,
Datacom/Lite n Easy,13/01/2025 0:00,12/02/2025 23:59,"January, 2025",Genesys Cloud Collaborate User,0,0,1,1,,,,,,,,,,,
Datacom/Lite n Easy,13/12/2024 0:00,12/01/2025 23:59,"December, 2024",Genesys Cloud CX 3,220,220,276,56,,12,672,,,,,,,,
Datacom/Lite n Easy,13/01/2025 0:00,12/02/2025 23:59,"January, 2025",Genesys Cloud CX 3,220,220,272,52,,12,624,,,,,,,,
Datacom/Lite n Easy,13/02/2025 0:00,12/03/2025 23:59,"February, 2025",Genesys Cloud CX 3,220,220,269,49,,12,588,,1884,*Matt confirm contract figure is 220,,,,,
Deakin,1/12/2024 0:00,31/12/2024 23:59,"December, 2024",Genesys Cloud CX 3 Concurrent,110,110,146,36,,,,,,,,,,,
Deakin,1/01/2025 0:00,31/01/2025 23:59,"January, 2025",Genesys Cloud CX 3 Concurrent,110,110,150,40,,12.5,500,,,,,,,,
Deakin,1/02/2025 0:00,28/02/2025 23:59,"February, 2025",Genesys Cloud CX 3 Concurrent,110,110,169,59,,12.5,737.5,,,,,,,,
Deakin,1/03/2025 0:00,31/03/2025 23:59,"March, 2025",Genesys Cloud CX 3 Concurrent,150,110,168,58,,12.5,725,,1962.5,*Matt please hard code the contracted figure of 110,,,,,
JLL,17/12/2024 0:00,16/01/2025 23:59,"January, 2025",Genesys Cloud CX 3 Concurrent,150,150,146,-4,,,,,,,,,,,
JLL,17/01/2025 0:00,16/02/2025 23:59,"February, 2025",Genesys Cloud CX 3 Concurrent,150,150,138,-12,,,,,,,,,,,
JLL,17/02/2025 0:00,16/03/2025 23:59,"March, 2025",Genesys Cloud CX 3 Concurrent,150,150,143,-7,,,,,,*Matt please hard code the contracted figure of 150,,,,,
Luxury Escapes,15/12/2024 0:00,14/01/2025 23:59,"December, 2024",Genesys Cloud CX 3,180,180,222,42,,,,LE has monthly billing,,,,,,,
Luxury Escapes,15/01/2025 0:00,14/02/2025 23:59,"January, 2025",Genesys Cloud CX 3,180,180,222,42,,,,LE has monthly billing,,,,,,,
Luxury Escapes,15/02/2025 0:00,14/03/2025 23:59,"February, 2025",Genesys Cloud CX 3,180,180,223,43,,,,LE has monthly billing,,*Matt please hard code the contracted figure of 180,,,,,
QPC/Anglicare,26/12/2024 0:00,25/01/2025 23:59,"January, 2025",Genesys Cloud CX 1 Concurrent,96,96,77,-19,,,,,,,,,,,
QPC/Anglicare,26/01/2025 0:00,25/02/2025 23:59,"February, 2025",Genesys Cloud CX 1 Concurrent,96,96,80,-16,,,,,,,,,,,
QPC/Anglicare,26/02/2025 0:00,25/03/2025 23:59,"March, 2025",Genesys Cloud CX 1 Concurrent,96,96,79,-17,,,,,,*Matt please hard code the contracted figure of 96,,,,,
QPC/BAT APAC,26/12/2024 0:00,25/01/2025 23:59,"January, 2025",Genesys Cloud Communicate User,20,20,0,-20,,,,,,,,,,,
QPC/BAT APAC,26/01/2025 0:00,25/02/2025 23:59,"February, 2025",Genesys Cloud Communicate User,20,20,0,-20,,,,,,,,,,,
QPC/BAT APAC,26/02/2025 0:00,25/03/2025 23:59,"March, 2025",Genesys Cloud Communicate User,20,20,0,-20,,,,,,,,,,,
QPC/BAT APAC,26/12/2024 0:00,25/01/2025 23:59,"January, 2025",Genesys Cloud CX 3,80,80,74,-6,,,,,,,,,,,
QPC/BAT APAC,26/01/2025 0:00,25/02/2025 23:59,"February, 2025",Genesys Cloud CX 3,80,80,76,-4,,,,,,,,,,,
QPC/BAT APAC,26/02/2025 0:00,25/03/2025 23:59,"March, 2025",Genesys Cloud CX 3,80,80,71,-9,,,,,,*Matt please hard code the contracted figure of 80,,,,,
QPC/BAT Pakistan,29/12/2024 0:00,28/01/2025 23:59,"January, 2025",Genesys Cloud CX 3,24,24,23,-1,,,,,,,,,,,
QPC/BAT Pakistan,29/01/2025 0:00,27/02/2025 23:59,"February, 2025",Genesys Cloud CX 3,24,24,24,0,,,,,,,,,,,
QPC/BAT Pakistan,28/02/2025 0:00,28/03/2025 23:59,"March, 2025",Genesys Cloud CX 3,24,24,21,-3,,,,,,*Matt please hard code the contracted figure of 50,,,,,
QPC/Chemist Warehouse,,,"January, 2025",,,35,50,15,,12,180,,,,,,,,
,,,"February, 2025",,,35,52,17,,12,204,,,,,,,,
,,,"March, 2025",,,35,56,21,,12,252,,636,*Matt please hard code the contracted figure of 35,,,,,
QPC/Compare the Market,,26/01/2025 0:00,,,,150,265,115,,4,460,,,,,,,,
,,26/02/2025 0:00,,,,150,284,134,,4,536,,,,,,,,
,,26/03/2025 0:00,,,,150,283,133,,4,532,,1528,*Matt please hard code the contracted figure of 150,,,,,
QPC/MYOB,1/12/2024 0:00,31/12/2024 23:59,"December, 2024",Genesys Cloud CX 3 Concurrent,500,500,448,-52,,,,MYOB has monthly billing,,,,,,,
QPC/MYOB,1/01/2025 0:00,31/01/2025 23:59,"January, 2025",Genesys Cloud CX 3 Concurrent,460,420,448,28,,,,MYOB has monthly billing,,,,,,,
QPC/MYOB,1/02/2025 0:00,28/02/2025 23:59,"February, 2025",Genesys Cloud CX 3 Concurrent,460,420,438,18,,,,MYOB has monthly billing,,,,,,,
QPC/MYOB,1/03/2025 0:00,31/03/2025 23:59,"March, 2025",Genesys Cloud CX 3 Concurrent,460,420,415,-5,,,,,,*Matt please hard code the contracted figure of 420,,,,,
QPC/PICA,5/12/2024 0:00,4/01/2025 23:59,"December, 2024",Genesys Cloud CX 2 Concurrent,50,55,59,4,,13.15,52.6,,,,,,,,
QPC/PICA,5/01/2025 0:00,4/02/2025 23:59,"January, 2025",Genesys Cloud CX 2 Concurrent,50,55,59,4,,13.15,52.6,,,,,,,,
QPC/PICA,5/02/2025 0:00,4/03/2025 23:59,"February, 2025",Genesys Cloud CX 2 Concurrent,50,55,55,0,,13.15,0,,105.2,,,,,,
QPC/PICA,5/03/2025 0:00,4/04/2025 23:59,"March, 2025",Genesys Cloud CX 2 Concurrent,50,55,54,-1,,,,,,*Matt please hard code the contracted figure of 55,,,,,
QPC/Ricoh Oceania,31/12/2024 0:00,30/01/2025 23:59,"January, 2025",Genesys Cloud CX 2,94,94,81,-13,,,,,,,,,,,
QPC/Ricoh Oceania,31/01/2025 0:00,27/02/2025 23:59,"February, 2025",Genesys Cloud CX 2,94,94,84,-10,,,,,,,,,,,
QPC/Ricoh Oceania,28/02/2025 0:00,30/03/2025 23:59,"March, 2025",Genesys Cloud CX 2,94,94,84,-10,,,,,,*Matt please hard code the contracted figure of 94,,,,,
QPC/Superloop,30/12/2024 0:00,29/01/2025 23:59,"January, 2025",Genesys Cloud Communicate User,91,91,24,-67,,,,Superloop has monthly billing,,,,,,,
QPC/Superloop,30/01/2025 0:00,27/02/2025 23:59,"February, 2025",Genesys Cloud Communicate User,91,91,31,-60,,,,Superloop has monthly billing,,,,,,,
QPC/Superloop,28/02/2025 0:00,29/03/2025 23:59,"March, 2025",Genesys Cloud Communicate User,91,91,25,-66,,,,Superloop has monthly billing,,*Matt please hard code the contracted figure of 91,,,,,
QPC/Superloop,30/12/2024 0:00,29/01/2025 23:59,"January, 2025",Genesys Cloud CX 3,290,290,482,192,,,,Superloop has monthly billing,,,,,,,
QPC/Superloop,30/01/2025 0:00,27/02/2025 23:59,"February, 2025",Genesys Cloud CX 3,290,290,504,214,,,,Superloop has monthly billing,,,,,,,
QPC/Superloop,28/02/2025 0:00,29/03/2025 23:59,"March, 2025",Genesys Cloud CX 3,290,290,491,201,,,,Superloop has monthly billing,,*Matt please hard code the contracted figure of 290,,,,,
QPC/Tabcorp,23/12/2024 0:00,22/01/2025 23:59,"January, 2025",Genesys Cloud Collaborate User,0,0,1,1,,,,,,,,,,,
QPC/Tabcorp,23/12/2024 0:00,22/01/2025 23:59,"January, 2025",Genesys Cloud CX 3 Concurrent,190,190,111,-79,,,,,,,,,,,
QPC/Tabcorp,23/01/2025 0:00,22/02/2025 23:59,"February, 2025",Genesys Cloud CX 3 Concurrent,0,0,107,107,,,,,,,,,,,
QPC/Tabcorp,23/02/2025 0:00,22/03/2025 23:59,"March, 2025",Genesys Cloud CX 3 Concurrent,0,0,118,118,,,,,,*Matt please hard code the contracted figure of 50,,,,,
QPC/UniSuper,16/12/2024 0:00,15/01/2025 23:59,"December, 2024",Genesys Cloud Collaborate User,0,0,1,1,,,,,,,,,,,
QPC/UniSuper,16/01/2025 0:00,15/02/2025 23:59,"January, 2025",Genesys Cloud Collaborate User,0,0,1,1,,,,,,,,,,,
QPC/UniSuper,16/12/2024 0:00,15/01/2025 23:59,"December, 2024",Genesys Cloud CX 3 Concurrent,150,150,153,3,,,,,,,,,,,
QPC/UniSuper,16/01/2025 0:00,15/02/2025 23:59,"January, 2025",Genesys Cloud CX 3 Concurrent,150,150,160,10,,,,,,,,,,,
QPC/UniSuper,16/02/2025 0:00,15/03/2025 23:59,"March, 2025",Genesys Cloud CX 3 Concurrent,150,150,162,12,,,,,,,,,,,
,,,,,,,,,,,,,,,,,,,
,,,,,,,,,,,,,10789.1,,,,,,
