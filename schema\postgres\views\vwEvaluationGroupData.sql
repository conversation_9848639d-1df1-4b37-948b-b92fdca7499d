-- Drop the table if it exists (this will be replaced by a view)
DROP TABLE IF EXISTS mvwevaluationgroupdata;
GO

-- Create view to replace the table (based on the logic from update_mvwevaluationgroupdata procedure)
CREATE OR REPLACE VIEW vwEvaluationGroupData AS
SELECT
    egd.keyid,
    egd.evaluationid,
    egd.questiongroupid,
    (SELECT ed.questiongroupname
        FROM evaldetails ed
        WHERE ed.questiongroupid::text = egd.questiongroupid::text
        LIMIT 1) AS questiongroupname,
    egd.totalscore,
    egd.maxtotalscore,
    egd.markedna,
    egd.totalcriticalscore,
    egd.maxtotalcriticalscore,
    egd.totalnoncriticalscore,
    egd.maxtotalnoncriticalscore,
    egd.totalscoreunweighted,
    egd.maxtotalscoreunweighted,
    egd.failedkillquestions,
    ud.divisionid,
    egd.comments,
    eda.conversationid
FROM evalquestiongroupdata egd
LEFT JOIN evaldata eda ON eda.evaluationid::text = egd.evaluationid::text
LEFT JOIN userdetails ud ON ud.id::text = eda.userid::text;
GO

-- Add comments for the view
COMMENT ON VIEW vwEvaluationGroupData IS 'View for evaluation group data (converted from table)';
COMMENT ON COLUMN vwEvaluationGroupData.keyid IS 'Primary Key';
COMMENT ON COLUMN vwEvaluationGroupData.evaluationid IS 'Evaluation GUID';
COMMENT ON COLUMN vwEvaluationGroupData.questiongroupid IS 'Question Group GUID';
COMMENT ON COLUMN vwEvaluationGroupData.questiongroupname IS 'Question Group Name';
COMMENT ON COLUMN vwEvaluationGroupData.totalscore IS 'Total Score';
COMMENT ON COLUMN vwEvaluationGroupData.maxtotalscore IS 'Max Total Score';
COMMENT ON COLUMN vwEvaluationGroupData.markedna IS 'Marked as NA';
COMMENT ON COLUMN vwEvaluationGroupData.totalcriticalscore IS 'Total Critical Score';
COMMENT ON COLUMN vwEvaluationGroupData.maxtotalcriticalscore IS 'Max Total Critical Score';
COMMENT ON COLUMN vwEvaluationGroupData.totalnoncriticalscore IS 'Total Non-Critical Score';
COMMENT ON COLUMN vwEvaluationGroupData.maxtotalnoncriticalscore IS 'Max Total Non-Critical Score';
COMMENT ON COLUMN vwEvaluationGroupData.totalscoreunweighted IS 'Total Score Unweighted';
COMMENT ON COLUMN vwEvaluationGroupData.maxtotalscoreunweighted IS 'Max Total Score Unweighted';
COMMENT ON COLUMN vwEvaluationGroupData.failedkillquestions IS 'Failed Kill Questions';
COMMENT ON COLUMN vwEvaluationGroupData.divisionid IS 'Division GUID';
COMMENT ON COLUMN vwEvaluationGroupData.comments IS 'Comments';
COMMENT ON COLUMN vwEvaluationGroupData.conversationid IS 'Conversation GUID';

-- Backward compatibility alias for existing queries
CREATE OR REPLACE VIEW mvwevaluationgroupdata AS
SELECT * FROM vwEvaluationGroupData;

COMMENT ON VIEW mvwevaluationgroupdata IS 'Backward compatibility alias for vwEvaluationGroupData - DEPRECATED: Use vwEvaluationGroupData instead';
