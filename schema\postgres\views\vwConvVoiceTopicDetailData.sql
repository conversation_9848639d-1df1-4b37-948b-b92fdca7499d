
-- Create regular view to replace the materialized view/table
CREATE OR REPLACE VIEW vwConvVoiceTopicDetailData AS
SELECT
    ct.keyid,
    ct.conversationid,
    ct.starttime,
    ct.starttimeltc,
    ct.participant,
    ct.duration,
    ct.confidence,
    ct.topicname,
    ct.topicid,
    ct.topicphrase,
    ct.transcriptphrase,
    ct.updated,
    cs.conversationstartdate,
    cs.conversationstartdateltc,
    cs.conversationenddate,
    cs.conversationenddateltc,
    cs.ttalkcomplete,
    cs.ani,
    cs.dnis,
    cs.firstmediatype,
    cs.divisionid,
    cs.firstqueueid,
    cs.firstqueuename,
    cs.lastqueueid,
    cs.lastqueuename,
    cs.firstagentid,
    cs.firstagentname,
    cs.firstagentdepartment AS firstagentdept,
    cs.firstagentmanager AS firstagentmanagerid,
    cs.firstagentmanager AS firstagentmanagername,
    cs.lastagentid,
    cs.lastagentname,
    cs.lastagentdepartment AS lastagentdept,
    cs.lastagentmanager AS lastagentmanagerid,
    cs.lastagentmanager AS lastagentmanagername,
    cs.firstwrapupcode,
    cs.firstwrapname AS firstwrapupname,
    cs.lastwrapupcode,
    cs.lastwrapname AS lastwrapupname,
    dd.name AS divisionname
FROM convvoicetopicdetaildata ct
LEFT JOIN vwconvsummarydata cs ON cs.conversationid::text = ct.conversationid::text
LEFT JOIN divisiondetails dd ON cs.divisionid::text = dd.id::text;

COMMENT ON VIEW vwConvVoiceTopicDetailData IS 'View for conversation voice topic detail data (converted from materialized view)';
