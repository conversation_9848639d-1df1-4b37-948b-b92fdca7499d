-- Backward compatibility alias for mvwconvvoicetopicdetaildata
-- This alias ensures existing queries continue to work after the mvw* to vw* conversion

-- Drop the table/view if it exists (replaced by regular view)
IF OBJECT_ID('mvwconvvoicetopicdetaildata', 'U') IS NOT NULL
    DROP TABLE mvwconvvoicetopicdetaildata;
IF OBJECT_ID('mvwconvvoicetopicdetaildata', 'V') IS NOT NULL
    DROP VIEW mvwconvvoicetopicdetaildata;
GO

-- Create alias view pointing to the new vwConvVoiceTopicDetailData
CREATE OR ALTER VIEW mvwconvvoicetopicdetaildata AS
SELECT * FROM vwConvVoiceTopicDetailData;
GO

-- Add extended property for documentation
EXEC sys.sp_addextendedproperty 
    @name = N'MS_Description', 
    @value = N'Backward compatibility alias for vwConvVoiceTopicDetailData - DEPRECATED: Use vwConvVoiceTopicDetailData instead',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'VIEW', @level1name = N'mvwconvvoicetopicdetaildata';
GO
