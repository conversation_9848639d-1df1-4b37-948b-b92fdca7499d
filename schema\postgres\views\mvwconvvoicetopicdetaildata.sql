-- Backward compatibility alias for mvwconvvoicetopicdetaildata
-- This alias ensures existing queries continue to work after the mvw* to vw* conversion

-- Drop the materialized view/table if it exists (replaced by regular view)
DROP MATERIALIZED VIEW IF EXISTS mvwconvvoicetopicdetaildata;
DROP TABLE IF EXISTS mvwconvvoicetopicdetaildata;

-- Create backward compatibility alias for existing queries
CREATE OR REPLACE VIEW mvwconvvoicetopicdetaildata AS
SELECT * FROM vwConvVoiceTopicDetailData;

COMMENT ON VIEW mvwconvvoicetopicdetaildata IS 'Backward compatibility alias for vwConvVoiceTopicDetailData - DEPRECATED: Use vwConvVoiceTopicDetailData instead';
