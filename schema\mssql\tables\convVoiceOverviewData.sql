IF dbo.csg_table_exists('convVoiceOverviewData') = 0
CREATE TABLE [convVoiceOverviewData](
    [keyid] [nvarchar](50) NOT NULL,
    [conversationid] [nvarchar](50),
    [sentimentscore] [decimal](20, 2),
    [sentimenttrend] [decimal](20, 2),
    [agentdurationpercentage] [decimal](20, 2),
    [customerDurationPercentage] [decimal](20, 2),
    [silenceDurationPercentage] [decimal](20, 2),
    [ivrDurationPercentage] [decimal](20, 2),
    [acdDurationPercentage] [decimal](20, 2),
    [otherDurationPercentage] [decimal](20, 2),
    [updated] [datetime],
    [overtalkdurationpercentage] [decimal](20, 2),
    [overtalkcount] [int],
    [sentimenttrendclass] [nvarchar](50),
    [phrasecount] [bigint],
	[peerid] [nvarchar](50),
	[gettransscript] [nvarchar](5),
	[transcript_processed] [bit],
	[transcript_processed_date] [datetime],
	[transcript_processed_notes] [nvarchar](255),
    CONSTRAINT [PK_convVoiceOverviewData] PRIMARY KEY ([keyid])
);

-- Indexes for performance optimization
IF dbo.csg_index_exists('convvoiceoverviewdata_conversationid_idx', 'convVoiceOverviewData') = 0
CREATE INDEX [convvoiceoverviewdata_conversationid_idx] ON [convVoiceOverviewData]([conversationid]);

IF dbo.csg_index_exists('convvoiceoverviewdata_updated_idx', 'convVoiceOverviewData') = 0
CREATE INDEX [convvoiceoverviewdata_updated_idx] ON [convVoiceOverviewData]([updated]);

-- Table modifications
IF dbo.csg_column_exists('convVoiceOverviewData', 'sentimenttrendclass') = 0
    ALTER TABLE convVoiceOverviewData ADD sentimenttrendclass nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN sentimenttrendclass nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'phrasecount') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD phrasecount int NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN phrasecount int NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'peerid') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD peerid nvarchar(50) NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN peerid nvarchar(50) NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'gettransscript') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD gettransscript nvarchar(5) NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN gettransscript nvarchar(5) NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'phrasecount') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD phrasecount bigint NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN phrasecount bigint NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'transcript_processed') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD transcript_processed bit NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN transcript_processed bit NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'transcript_processed_date') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD transcript_processed_date datetime NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN transcript_processed_date datetime NULL;

IF dbo.csg_column_exists('convVoiceOverviewData', 'transcript_processed_notes') = 0
    ALTER TABLE dbo.convVoiceOverviewData ADD transcript_processed_notes nvarchar(255) NULL;
ELSE
    ALTER TABLE convVoiceOverviewData ALTER COLUMN transcript_processed_notes nvarchar(255) NULL;

