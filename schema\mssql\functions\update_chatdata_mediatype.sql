-- Stored procedure to update mediatype column in chatdata table
-- This procedure identifies the media type (chat vs message) for conversations in chatdata
-- by querying only the relevant conversationids from detailedinteractiondata table
-- Performance optimized: only queries detailedinteractiondata for existing chatdata records

IF OBJECT_ID('update_chatdata_mediatype', 'P') IS NOT NULL
    DROP PROCEDURE update_chatdata_mediatype;
GO

CREATE PROCEDURE update_chatdata_mediatype
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @UpdatedRows INT = 0;
    DECLARE @StartTime DATETIME = GETDATE();
    
    PRINT 'Starting optimized mediatype update for chatdata table at ' + CONVERT(VARCHAR, @StartTime, 120);
    PRINT 'Only querying detailedinteractiondata for conversations that exist in chatdata with NULL mediatype';
    
    -- Update mediatype for records where it's currently NULL
    -- Only query detailedinteractiondata for conversations that exist in chatdata
    UPDATE c
    SET mediatype = CASE
        WHEN d.mediatype = 'chat' THEN 'chat'
        WHEN d.mediatype = 'message' THEN 'message'
        ELSE 'unknown'
    END
    FROM chatdata c
    INNER JOIN (
        -- Get the most recent media type for conversations that exist in chatdata
        SELECT
            d.conversationid,
            d.mediatype,
            ROW_NUMBER() OVER (PARTITION BY d.conversationid ORDER BY d.conversationstartdate DESC) as rn
        FROM detailedinteractiondata d
        INNER JOIN chatdata c_filter ON d.conversationid = c_filter.conversationid
        WHERE d.mediatype IN ('chat', 'message')
        AND c_filter.mediatype IS NULL
    ) d ON c.conversationid = d.conversationid AND d.rn = 1
    WHERE c.mediatype IS NULL;
    
    SET @UpdatedRows = @@ROWCOUNT;
    
    DECLARE @EndTime DATETIME = GETDATE();
    DECLARE @Duration INT = DATEDIFF(SECOND, @StartTime, @EndTime);
    
    PRINT 'Completed mediatype update. Updated ' + CAST(@UpdatedRows AS VARCHAR) + ' rows in ' + CAST(@Duration AS VARCHAR) + ' seconds.';
    
    -- Log summary of current mediatype distribution
    SELECT 
        mediatype,
        COUNT(*) as record_count
    FROM chatdata
    GROUP BY mediatype
    ORDER BY mediatype;
    
END;
GO

-- Grant execute permissions
GRANT EXECUTE ON update_chatdata_mediatype TO PUBLIC;
GO
