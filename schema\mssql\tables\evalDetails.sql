IF dbo.csg_table_exists('evalDetails') = 0
CREATE TABLE [evalDetails](
    [id] [nvarchar](400) NOT NULL,
    [evaluationid] [nvarchar](50),
    [evaluationformid] [nvarchar](50),
    [evaluationname] [nvarchar](200),
    [questiongroupid] [nvarchar](50),
    [questiongroupname] [nvarchar](200),
    [questiongroupToHighest] [bit],
    [questiongroupToNA] [bit],
    [questiongroupwieght] [decimal](20, 2),
    [questiongroupmanwieght] [bit],
    [questionid] [nvarchar](50),
    [questiontext] [nvarchar](400),
    [questionhelptext] [nvarchar](max),
    [quesiontype] [nvarchar](50),
    [questionnaenabled] [bit],
    [questioncommentsreq] [bit],
    [questioniskill] [bit],
    [questioniscritical] [bit],
    [questionanwserid] [nvarchar](50),
    [questionanswertext] [nvarchar](200),
    [questionanswervalue] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK__evalDeta__3213E83F77D1AF0A] PRIMARY KEY ([id])
);

-- Create index on evaluationformid if it doesn't exist
IF dbo.csg_index_exists('evaldetailsformsid', 'evalDetails') = 0
CREATE INDEX [evaldetailsformsid] ON [evalDetails] ([evaluationformid]);

-- Additional indexes for evaluation view performance
IF dbo.csg_index_exists('evaldetails_questiongroupid_idx', 'evalDetails') = 0
CREATE INDEX [evaldetails_questiongroupid_idx] ON [evalDetails]([questiongroupid]);

-- Composite index for evaluation question data view joins
IF dbo.csg_index_exists('evaldetails_composite_idx', 'evalDetails') = 0
CREATE INDEX [evaldetails_composite_idx] ON [evalDetails]([evaluationformid], [questiongroupid], [questionid], [questionanwserid]);

-- Remove MS_Description property for evaluationid if it exists
-- This prevents the error during installation
IF dbo.csg_column_description_exists('evalDetails', 'evaluationid') = 1
BEGIN
    EXEC sys.sp_dropextendedproperty
    @name = N'MS_Description',
    @level0type = N'SCHEMA', @level0name = N'dbo',
    @level1type = N'TABLE', @level1name = N'evalDetails',
    @level2type = N'COLUMN', @level2name = N'evaluationid';
END
